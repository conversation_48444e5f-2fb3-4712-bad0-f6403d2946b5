<?php

namespace App\Services;

use App\Helpers\Helpers;
use App\Repositories\CommonRepository;
use App\Repositories\StudentSummaryTabRepository;
use App\Traits\CommonTrait;
use App\Traits\ResponseTrait;
use Domains\Moodle\Traits\MoodleStatusTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Integrations\Zoho\Traits\ZohoTrait;
use Support\Services\UploadService;

class StudentSummaryTabService extends CommonRepository
{
    use CommonTrait;
    use MoodleStatusTrait;
    use ResponseTrait;
    use ZohoTrait;

    private $studentSummaryTabRepository;

    public function __construct(StudentSummaryTabRepository $studentSummaryTabRepository)
    {
        $this->studentSummaryTabRepository = $studentSummaryTabRepository;
    }

    public function getActivityLog($request)
    {
        $dataArr = $this->studentSummaryTabRepository->getActivityLog($request);
        $data = [];
        foreach ($dataArr as $value) {
            $activityMonth = $value['activity_month'];
            if (! isset($data[$activityMonth])) {
                $data[$activityMonth] = [
                    'date' => $activityMonth,
                    'activity_type' => [],
                    'showdetails' => [],
                ];
            }
            $data[$activityMonth]['activity_type'][] = $value['activity_log'];
            $showDetails = self::createActivityLogDetails($value);
            $data[$activityMonth]['showdetails'][] = $showDetails;
        }

        return array_values($data);
    }

    private static function createActivityLogDetails(array $value): array
    {
        return [
            'id' => $value['id'],
            'created_at' => $value['created_at'],
            'updated_at' => $value['updated_at'],
            'type' => $value['type'],
            'status' => $value['status'],
            'status_type' => $value['status_type'],
            'activity_log' => $value['activity_log'],
            // 'date_time' => date("M d, Y h:i A", strtotime($value['created_at'])),
            'date_time' => Helpers::convertDateTimeToReadableFormat($value['created_at']),
            'description' => $value['log'],
            'note_attachment' => ($value['note_attachment'] == null) ? '' : $value['note_attachment'],
            'log_updated_properties' => (isset($value['log_updated_properties'])) ? $value['log_updated_properties'] : '',
            'note_attachment_path' => $value['note_attachment_path'],
            'note_attachment_url' => $value['note_attachment'] ? UploadService::download($value['note_attachment_path'].$value['note_attachment']) : '',
            'note_attachment_preview' => $value['note_attachment'] ? UploadService::url($value['note_attachment_path'].$value['note_attachment']) : '',
            'note_attachment_type' => substr($value['note_attachment'], strpos($value['note_attachment'], '.') + 1),
            'user_name' => $value['user_name'],
        ];
    }

    public function getCourseProgressTimeline($studentCourseId): array
    {
        $courseDetail = $this->studentSummaryTabRepository->getCourseDetail($studentCourseId);
        $courseProgress = $this->studentSummaryTabRepository->getCourseProgress(['a1.college_id' => $courseDetail['college_id'], 'a1.student_id' => $courseDetail['student_id'], 'a1.course_id' => $courseDetail['course_id'], 'a1.student_course_id' => $courseDetail['id']]);
        $data = [];
        foreach ($courseProgress as $row) {
            if (! empty($row['activity_start_date'])) {
                $data[] = [
                    'unit_name' => $row['unit_name'],
                    'final_outcome' => ! empty($row['final_outcome']) ? $row['final_outcome'] : '',
                    'date' => ! empty($row['activity_start_date']) ? date('Y-m-dTH:i:s.000Z', strtotime($row['activity_start_date'])) : '', // TODO::R&D
                    'activity_start_date' => Helpers::convertDateToReadableFormat($row['activity_start_date']),   // !empty($row['activity_start_date']) ? date("M d Y", strtotime($row['activity_start_date'])) : "",
                    'activity_finish_date' => Helpers::convertDateToReadableFormat($row['activity_finish_date']),  // !empty($row['activity_finish_date']) ? date("M d Y", strtotime($row['activity_finish_date'])) : "",
                    'unit_code' => ! empty($row['unit_code']) ? $row['unit_code'] : '',
                ];
            }
        }

        return $data;
    }

    public function getStudentSummaryData($request)
    {
        $finalResult = [];
        $studentCourseId = $request->student_course_id;
        $courseDetail = $this->studentSummaryTabRepository->getCourseDetails($studentCourseId);
        if ($courseDetail) {
            $arrCourseDurationType = Config::get('constants.arrCourseDurationType');
            $courseDurationType = $courseDetail->course_duration_type ?? '';
            if ($courseDurationType) {
                $courseDetail->course_duration_type = isset($arrCourseDurationType[$courseDurationType]) ? $arrCourseDurationType[$courseDurationType] : '';
            } else {
                $courseDetail->course_duration_type = 'Weeks';
            }

            $finalResult['course_detail'] = $courseDetail;
            $collegeId = $courseDetail['college_id'];
            $studentId = $courseDetail['student_id'];
            $courseId = $courseDetail['course_id'];
            $agentId = $courseDetail['agent_id'];
            $whereArr = ['a1.college_id' => $collegeId, 'a1.student_id' => $studentId, 'a1.course_id' => $courseId, 'a1.student_course_id' => $courseDetail['id']];

            $attendanceResult = $this->getStudentAttendanceData($whereArr);
            $finalResult['course_progress'] = $this->studentSummaryTabRepository->getCourseProgress($whereArr);
            $finalResult['current_course'] = $this->studentSummaryTabRepository->getCurrentCourse($whereArr);
            $finalResult['course_payment'] = $this->studentSummaryTabRepository->getCoursePayment($studentCourseId, $courseDetail->course_fee);
            $finalResult['attendance_chart_data'] = $this->studentSummaryTabRepository->getStudentCourseAttendanceData($collegeId, $studentId, $courseId);
            $finalResult['student_task_list'] = $this->studentSummaryTabRepository->getStudentTaskData($collegeId, $studentId);
            $finalResult['studentIntervention'] = $this->studentSummaryTabRepository->getStudentIntervention($collegeId, $studentId, $courseId);
            $finalResult['studentSanction'] = $this->studentSummaryTabRepository->getStudentSanction($collegeId, $studentId);
            $finalResult['course_attendance'] = [$attendanceResult['calculated_attendance']];

            $finalResult['course_timeline'] = array_map(function ($row) {
                if (! empty($row['activity_start_date'])) {
                    return [
                        'unit_name' => $row['unit_name'],
                        'final_outcome' => $row['final_outcome'] ?? '',
                        'date' => ($row['activity_start_date'] == '0000-00-00') ? '' : date('Y-m-dTH:i:s.000Z', strtotime($row['activity_start_date'])),
                        'activity_start_date' => ($row['activity_start_date'] == '0000-00-00') ? '' : Helpers::convertDateToReadableFormat($row['activity_start_date']),
                        'activity_finish_date' => ($row['activity_finish_date'] == '0000-00-00') ? '' : Helpers::convertDateToReadableFormat($row['activity_finish_date']),
                        'unit_code' => $row['unit_code'] ?? '',
                    ];
                }
            }, $finalResult['course_progress']);
        }

        return $finalResult;
    }

    public function createLogFromTimeline($request)
    {
        $file = $request->file('note_attachment');
        $note_attachment_path = '';
        $note_attachment = '';
        if (! empty($file)) {
            $fileUploadResult = $this->uploadFile($file);
            if ($fileUploadResult) {
                $note_attachment_path = $fileUploadResult['path'];
                $note_attachment = $fileUploadResult['name'];
            }
        }
        $logData = [
            'college_id' => $request->user()->college_id,
            'comment_by' => $request->user()->id,
            'student_id' => $request->input('student_id'),
            'today_date' => date('l,d F Y'),
            'type' => '',
            'log_type' => 'note',
            'status' => '',
            'log' => $request->input('log'),
            'activity_log' => 'note',
            'note_attachment_path' => $note_attachment_path,
            'note_attachment' => $note_attachment,
            'view_by' => '',
            'visiblity' => 1,
            'created_by' => $request->user()->id,
            'updated_by' => $request->user()->id,
        ];

        return $this->studentSummaryTabRepository->saveActivityNote($logData);
    }

    public function uploadFile($file)
    {
        $filePath = Config::get('constants.uploadFilePath.StudentNotes');
        $destinationPath = Helpers::changeRootPath($filePath);
        $time = time();
        $originalName = $file->getClientOriginalName();
        $filenm = hashFileName($originalName);
        $fileName = $this->removeSpecialCharacter($filenm);
        // $upload_success = $file->move($destinationPath['default'], $fileName);
        $upload_success = UploadService::uploadAs($destinationPath['view'], $file, $fileName);
        info('file uploaded form Activity log Tab', [$upload_success]);
        if ($upload_success) {
            return [
                'path' => $destinationPath['view'],
                'name' => $fileName,
            ];
        }

        return null;
    }

    private function removeSpecialCharacter($fileName)
    {
        $fileName = str_replace(' ', '-', $fileName); // Replaces all spaces with hyphens.

        return preg_replace('/[^A-Za-z0-9.\-]/', '', $fileName); // Removes special chars.
    }

    public function createLog($request)
    {
        $file = $request->file('note_attachment1');
        $note_attachment_path = '';
        $note_attachment = '';
        if (! empty($file)) {
            $fileUploadResult = $this->uploadFile($file);
            if ($fileUploadResult) {
                $note_attachment_path = $fileUploadResult['path'];
                $note_attachment = $fileUploadResult['name'];
            }
        }
        $logData = [
            'college_id' => $request->user()->college_id,
            'comment_by' => $request->user()->id,
            'student_id' => $request->input('note_student_id'),
            'today_date' => date('l,d F Y'),
            'type' => '',
            'log_type' => 'note',
            'status' => '',
            'log' => $request->input('log'),
            'activity_log' => 'note',
            'note_attachment_path' => $note_attachment_path,
            'note_attachment' => $note_attachment,
            'view_by' => '',
            'visiblity' => 1,
            'created_by' => $request->user()->id,
            'updated_by' => $request->user()->id,
        ];

        return $this->studentSummaryTabRepository->saveActivityNote($logData);
    }

    public function deleteActivityNoteFromTimeLine($id)
    {
        return $this->studentSummaryTabRepository->deleteActivityNote($id);
    }

    public function editActivityNote(Request $request)
    {
        $file = $request->file('note_attachment_edit');
        $note_attachment_path = '';
        $note_attachment = '';
        if (! empty($file)) {
            $filePath = Config::get('constants.uploadFilePath.StudentNotes');
            $destinationPath = Helpers::changeRootPath($filePath);
            $time = time();
            $originalName = $file->getClientOriginalName();
            $filenm = hashFileName($originalName);
            $fileName = $this->removeSpecialCharacter($filenm);
            $upload_success = $file->move($destinationPath['default'], $fileName);
            if ($upload_success) {
                $note_attachment_path = $destinationPath['view'];
                $note_attachment = $fileName;
            }
        }

        return $this->studentSummaryTabRepository->updateActivityNote($request->id, ['log' => $request->log, 'note_attachment_path' => $note_attachment_path, 'note_attachment' => $note_attachment, 'updated_by' => $request->user()->id]);
    }

    public function getActivityLogDataWithFlag($request)
    {

        $dataArr = $this->studentSummaryTabRepository->getActivityLogTypeData($request);
        $final = $this->convertToISO8859_1($dataArr);

        $data = [];
        if (! empty($final['data'])) {
            foreach ($final['data'] as $value) {
                $activityMonth = $value['activity_month'];
                if (! isset($data[$activityMonth])) {
                    $data[$activityMonth] = [
                        'date' => $activityMonth,
                        'activity_type' => [],
                        'showdetails' => [],
                    ];
                }
                $data[$activityMonth]['activity_type'][] = $value['activity_log'];
                $showDetails = self::createActivityLogDetails($value);

                $data[$activityMonth]['showdetails'][] = $showDetails;
            }
        }

        return array_values($data);

    }

    private function convertToISO8859_1($array)
    {
        foreach ($array as &$item) {
            if (is_array($item)) {
                $item = $this->convertToISO8859_1($item); // Recursive call for nested arrays
            } elseif (is_string($item)) {
                $item = mb_convert_encoding($item, 'UTF-8', 'auto');

            }
        }

        return $array;
    }

    public function getResultTabData(Request $request)
    {
        return $this->studentSummaryTabRepository->getResultTabSummary($request);
    }

    public function studentResultUnitData($request)
    {
        return [
            'data' => $this->studentSummaryTabRepository->studentResultUnitData($request),
            'total' => $this->studentSummaryTabRepository->studentResultUnitData($request, true),
        ];
    }
}
