<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rto_course_extension_history', function (Blueprint $table) {
            $table->date('new_start_date')->nullable()->after('start_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rto_course_extension_history', function (Blueprint $table) {
            $table->dropColumn('new_start_date');
        });
    }
};
