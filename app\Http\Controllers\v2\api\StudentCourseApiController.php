<?php

namespace App\Http\Controllers\v2\api;

use App\Exceptions\ApplicationException;
use App\Http\Controllers\Controller;
use App\Http\Requests\FormValidation\AdditionalServiceRequest;
use App\Http\Requests\FormValidation\OfferCommunicationRequest;
use App\Http\Requests\FormValidation\OhscRequest;
use App\Http\Requests\FormValidation\StudentCertificate;
use App\Http\Requests\FormValidation\StudentProfile\EditEnrollResultCourseRequest;
use App\Http\Requests\FormValidation\StudentProfile\EditEnrollStudentCourseRequest;
use App\Http\Requests\FormValidation\StudentProfile\ExtendCourseDateRangeRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveStudentTrainingPlanRequest;
use App\Http\Requests\FormValidation\StudentProfile\SaveSubjectToCourseRequest;
use App\Http\Requests\FormValidation\StudentProfile\UpdateStudentTrainingPlanRequest;
use App\Model\v2\CertificateTemplate;
use App\Model\v2\ContractCode;
use App\Model\v2\Employer;
use App\Model\v2\Student;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentSubjectEnrolment;
use App\Process\StudentProfile\AddCoeForOfferProcess;
use App\Process\StudentProfile\ManageOfferScheduleProcess;
use App\Repositories\StudentProfileRepository;
use App\Services\StudentCourseService;
use App\Services\StudentSummaryTabService;
use App\Traits\ResponseTrait;
use Carbon\Carbon;
use Domains\Students\DTO\ReschedulePayload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class StudentCourseApiController extends Controller
{
    use ResponseTrait;

    private $studentCourseService;

    private $studentCourse;

    private $studentSubjectEnrolment;

    private $employer;

    private $contractCode;

    private $studentSummaryTabService;

    public function __construct(
        StudentCourseService $studentCourseService,
        StudentCourses $studentCourseModel,
        StudentSubjectEnrolment $studentSubjectEnrolment,
        Employer $employer,
        ContractCode $contractCode,
        StudentSummaryTabService $studentSummaryTabService
    ) {
        $this->studentCourseService = $studentCourseService;
        $this->studentCourse = new StudentProfileRepository($studentCourseModel);
        $this->studentSubjectEnrolment = new StudentProfileRepository($studentSubjectEnrolment);
        $this->employer = new StudentProfileRepository($employer);
        $this->contractCode = new StudentProfileRepository($contractCode);
        $this->studentSummaryTabService = $studentSummaryTabService;
        ini_set('memory_limit', '-1');
    }

    public function getCertificateIssueList(Request $request)
    {
        $data = $this->studentCourseService->getCertificateIssueListData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getEnrollSubjectList(Request $request)
    {
        $data = $this->studentCourseService->getEnrollSubjectListData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getEnrollSubSemester(Request $request)
    {
        $data = $this->studentCourseService->getEnrollSubSemesterList($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getEnrollVenue(Request $request)
    {
        $data['studentDetail'] = Student::find($request->student_id);
        $arrFundingSource = Config::get('constants.arrFundingSource');
        $data['arrFundingSource'] = $this->convertKendoFormat($arrFundingSource);

        $arrFundingSourceNat = Config::get('constants.arrFundingSourceNat');
        $data['arrFundingSourceNat'] = $this->convertKendoFormat($arrFundingSourceNat);

        $arrDeliveryMode = Config::get('constants.arrDeliveryMode');
        $data['arrDeliveryMode'] = $this->convertKendoFormat($arrDeliveryMode);

        $arrSelectFinalOutcomeNew = Config::get('constants.arrSelectFinalOutcomeNew');
        $data['arrSelectFinalOutcomeNew'] = $this->convertKendoFormat($arrSelectFinalOutcomeNew);

        $data['venueData'] = $this->studentCourseService->getEnrollVenueLocation($request);
        $data['studyReasonData'] = $this->studentCourseService->getStudyReason();
        $data['markOutcomeData'] = $this->studentCourseService->getMarkOutcomeData($request);

        $objStudentCourse = StudentCourses::where('id', $request->student_course_id)->first();

        $data['defaultSelected'] = [
            'delivery_mode' => $objStudentCourse->internal.$objStudentCourse->external.$objStudentCourse->workplace_based_delivery,
        ];

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getEnrolledCourse(Request $request)
    {

        $data['enrolledCourse'] = $this->studentCourseService->getEnrolledCourse($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function updateResultCourse(EditEnrollResultCourseRequest $request)
    {

        $data['updatedEnrollement'] = $this->studentCourseService->updateResultCourse($request->DTO());
        if ($data['updatedEnrollement']['status'] == 'error') {
            return $this->errorResponse($data['updatedEnrollement']['message'], 'data', $data, 200);
        } else {
            return $this->successResponse($data['updatedEnrollement']['message'], 'data', $data);
        }
    }

    public function getStudyReason(Request $request)
    {
        $data = $this->studentCourseService->getStudyReason();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getMarkOutcomeData(Request $request)
    {
        $data = $this->studentCourseService->getMarkOutcomeData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getTermList(Request $request)
    {
        $data = $this->studentCourseService->getTermList($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getTermDetails(Request $request)
    {
        $data = $this->studentCourseService->getTermDetails($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseFinishDate(Request $request)
    {
        $courseFinishDate = StudentCourses::where('id', $request->student_course_id)->value('finish_date');

        return $this->successResponse('Data found successfully', 'data', Carbon::createFromFormat('Y-m-d', $courseFinishDate)->format('d-m-Y'));
    }

    public function saveStudentSubjectEnrollment(SaveSubjectToCourseRequest $request)
    {
        $isHigherEd = ($request->is_higher_ed == 'true') ? true : false;

        return $this->studentCourseService->saveStudentSubjectEnrollment($request->DTO(), $isHigherEd);
    }

    public function getStudentCourseDetails(Request $request)
    {
        $data = $this->studentCourseService->getStudentCourseDetails($request);
        $data = utf8ize($data);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function updateEnrollStudentCourse(EditEnrollStudentCourseRequest $request)
    {
        $result = $this->studentCourseService->updateEnrollStudentCourse($request->DTO());
        if ($result['status'] == 'success') {
            return $this->successResponse($result['message'], 'data', $result);
        } else {
            return $this->errorResponse($result['message'], 'data', $result);
        }
    }

    public function getOshcProvider(Request $request)
    {
        $resultArr = $this->studentCourseService->getOshcProvider($request);

        return $this->successResponse('Data found successfully', 'data', $resultArr);
    }

    public function getOshcType(Request $request)
    {
        $resultArr = $this->studentCourseService->getOshcType($request);

        return $this->successResponse('Data found successfully', 'data', $resultArr);
    }

    public function getOshcDuration(Request $request)
    {
        $resultArr = $this->studentCourseService->getOshcDuration($request);

        return $this->successResponse('Data found successfully', 'data', $resultArr);
    }

    public function getOshcInfo(Request $request)
    {
        $resultArr = $this->studentCourseService->getOshcInfo($request->student_id);

        return $this->successResponse('Data found successfully', 'data', $resultArr);
    }

    public function getStudentOshcData(Request $request)
    {
        $resultArr = $this->studentCourseService->getStudentOshcData($request);

        return $this->successResponse('Data found successfully', 'data', $resultArr);
    }

    public function saveStudentOshcServiceData(OhscRequest $request)
    {
        $result = $this->studentCourseService->saveStudentOshcServiceData($request->DTO());

        return $this->successResponse('Data saved successfully', 'data', $result);
    }

    public function getServicesCategory(Request $request)
    {
        $resultArr = $this->studentCourseService->getServicesCategory($request);

        return $this->successResponse('Data found successfully', 'data', $resultArr);
    }

    public function getFacilityName(Request $request)
    {
        $resultArr = $this->studentCourseService->getFacilityName($request);

        return $this->successResponse('Data found successfully', 'data', $resultArr);
    }

    public function getServiceProvider(Request $request)
    {
        $resultArr = $this->studentCourseService->getServiceProvider($request);

        return $this->successResponse('Data found successfully', 'data', $resultArr);
    }

    public function getProviderPrice(Request $request)
    {
        $resultArr = $this->studentCourseService->getProviderPrice($request);

        return $this->successResponse('Data found successfully', 'data', $resultArr);
    }

    public function getAdditionalServiceData(Request $request)
    {
        $resultArr = $this->studentCourseService->getAdditionalServiceData($request);

        return $this->successResponse('Data found successfully', 'data', $resultArr);
    }

    public function getAdditionalServiceList(Request $request)
    {
        $resultArr = $this->studentCourseService->getAdditionalServiceListData($request);

        return $this->successResponse('Data found successfully', 'data', $resultArr);
    }

    public function saveStudentAdditionalService(AdditionalServiceRequest $request)
    {
        $data = $this->studentCourseService->saveStudentAdditionalService($request->input());

        return $this->successResponse('Save Successfully', 'data', $data);
        // echo json_encode($result);exit;
    }

    public function deleteAdditionalService(Request $request)
    {
        $result = $this->studentCourseService->deleteAdditionalServiceData($request);
        echo json_encode($result);
        exit;
    }

    public function getCoeHistoryList(Request $request)
    {
        $data = $this->studentCourseService->getCoeHistoryListData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseExtensionHistory(Request $request)
    {
        $data = $this->studentCourseService->getCourseExtensionHistoryData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function extendCourseDateRange(ExtendCourseDateRangeRequest $request)
    {
        $result = $this->studentCourseService->extendCourseDateRange($request->DTO());
        if ($result['status'] == 'success') {
            return $this->successResponse($result['message'], 'data', $result['data']);
        } else {
            return $this->errorResponse($result['message'], 'data', $result['data']);
        }
    }

    public function getStudentTrainingPlanList(Request $request)
    {
        $data = $this->studentCourseService->getStudentTrainingPlanListData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getTrainingPlanData(Request $request)
    {
        $resultArr = $this->studentCourseService->getTrainingPlanData($request);

        return $this->successResponse('Data found successfully', 'data', $resultArr);
    }

    public function saveTrainingPlanData(SaveStudentTrainingPlanRequest $request)
    {
        $data = $this->studentCourseService->saveTrainingPlanData($request->DTO());

        return $this->successResponse('Course Training Plan Add Successfully', 'data', $data);
    }

    public function updateTrainingPlanData(UpdateStudentTrainingPlanRequest $request)
    {
        $data = $this->studentCourseService->updateTrainingPlanData($request->DTO());

        return $this->successResponse('Course Training Plan Update Successfully', 'data', $data);
    }

    public function deleteTrainingPlanData(Request $request)
    {
        $data = $this->studentCourseService->deleteTrainingPlanData($request);

        return $this->successResponse('Course Training Plan Delete Successfully.', '', $data);
    }

    public function getFeeScheduleList(Request $request)
    {
        $data = $this->studentCourseService->getFeeScheduleListData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getFeeScheduleDetail(Request $request)
    {
        $data = $this->studentCourseService->getFeeScheduleDetailsData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getStudentOfferChecklist(Request $request)
    {
        $data = $this->studentCourseService->getStudentOfferChecklist($request);

        return $this->successResponse('Successfully.', 'data', $data);
    }

    public function uploadedOfferChecklistDocumentsData(Request $request)
    {
        $data = $this->studentCourseService->getUploadedOfferChecklistDocumentsData($request);

        return $this->successResponse('data found successfully', 'data', ['data' => $data, 'total' => count($data)]);
    }

    public function uploadOfferChecklistDocument(Request $request)
    {
        return $this->studentCourseService->uploadOfferChecklistDocument($request);
    }

    public function generateAgentInvoicePdf(Request $request)
    {
        return $this->studentCourseService->generateAgentInvoicePdf($request);
    }

    public function previewOfferLetter(Request $request)
    {
        $data = $this->studentCourseService->previewOfferLetter($request);

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function saveStudentCertificate(StudentCertificate $request)
    {
        $data = $this->studentCourseService->saveStudentCertificate($request->input());

        return $this->successResponse('Certificate Register Successfully', 'data', $data);
    }

    public function deleteStudentCertificate(Request $request)
    {
        $data = $this->studentCourseService->deleteStudentCertificate($request->input('id'));

        return $this->successResponse('Certificate Deleted Successfully', 'data', $data);
    }

    public function uploadStudentCertificate(Request $request)
    {
        $data = $this->studentCourseService->uploadStudentCertificate($request);

        return $this->successResponse('Certificate Upload Successfully', 'uploaded', $data);
    }

    public function downloadStudentCertificate(Request $request)
    {
        $data = $this->studentCourseService->downloadStudentCertificate($request->input('id'));
        if ($data) {
            return $this->successResponse('Data found successfully', 'data', $data);
        } else {
            return $this->errorResponse('Certificate not found', 'data', '', 200);
        }
    }

    /* Below functions are not re-structured */
    public function saveUpfrontFeeSchedule(Request $request)
    {
        // dd($_POST);
        $post = $request->input();
        $collegeId = $post['college_id'];
        $studentId = $post['student_id'];
        $selectedCourseId = $post['student_course_id'];
        $isSave = $request->input('is_save');
        $formData = $request->input('formData');
        $post['course_id'] = StudentCourses::find($post['student_course_id'])->course_id;

        $result = $this->studentCourseService->saveUpfrontFeeSchedule($post['college_id'], $post['student_id'], $post['course_id'], $post['student_course_id'], $isSave, $formData);

        return response()->json($result);
    }

    public function deleteOfferFeeSchedule(Request $request)
    {
        if ($request['type'] == 'bulk') {
            $this->studentCourseService->bulkDeleteOfferFeeScheduleData($request->all());
        } else {
            $this->studentCourseService->deleteOfferFeeScheduleData($request->all());
        }

        return $this->successResponse('Delete successfully', 'data', '');
    }

    public function getOfferId(Request $request)
    {
        $data = $this->studentCourseService->getOfferId($request->all());

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function getSectionTypeList(Request $request)
    {
        $data = $this->studentCourseService->getSectionTypeListData($request);

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function addOfferCommunicationLog(OfferCommunicationRequest $request)
    {
        $data = $this->studentCourseService->addOfferCommunicationLogData($request->DTO());

        return $this->successResponse('Offer Communication Log saved successfully', 'data', $data);
    }

    public function getOfferCommunicationLogList(Request $request)
    {
        $data = $this->studentCourseService->getOfferCommunicationLog($request);

        return $this->successResponse('Data Found', 'data', $data);
    }

    public function getCourseTabData(Request $request)
    {
        $request->course_id = $this->studentCourse->find($request->student_course_id)->course_id;
        $generateCertificateList = Config::get('constants.generateCertificateList');
        if ($request->is_higher_ed == 'false') {
            // Remove last 2 certificate because it's only use for HigherEd
            array_splice($generateCertificateList, -2);
        }
        $data['courseSummary'] = $this->studentSubjectEnrolment->getCurrentCourseSummary($request);
        $data['courseProgress'] = $this->studentSummaryTabService->getCourseProgressTimeline($request->input('student_course_id'));
        $data['generateCertificateList']['data'] = $this->convertKendoFormat($generateCertificateList);

        $generateCertificateList = CertificateTemplate::get()->pluck('name', 'id');
        $data['generateCertificateBetaList']['data'] = $this->convertKendoFormat($generateCertificateList);

        $data['getEmployerNameList']['data'] = $this->employer->selectData(['id as Id', 'employer_name as Name']);
        $data['getEmployerNameList']['data'] = $this->contractCode->selectData(['id as Id', 'contract_code as Name']);

        return $this->successResponse('Data Found', 'data', $data);
    }

    public function getCertificateType(Request $request)
    {

        $generateCertificateList = Config::get('constants.generateCertificateList');

        $data['generateCertificateList']['data'] = $this->convertKendoFormat($generateCertificateList);

        return $this->successResponse('Data Found', 'data', $data);
    }

    private function convertKendoFormat($resArr, $param1 = 'Id', $param2 = 'Name', $isSameValue = false)
    {
        $data = [];
        if ($resArr) {
            // unset($resArr['']);
            foreach ($resArr as $key => $value) {
                $data[] = [
                    $param1 => ($isSameValue) ? $value : $key,
                    $param2 => $value,
                ];
            }
        }

        return $data;
    }

    public function deleteOfferCommunicationLog(Request $request)
    {
        $data = $this->studentCourseService->deleteOfferCommunicationLog($request->input());
        if ($data) {
            return ['status' => 'success', 'message' => 'Delete Successfully.'];
        } else {
            return ['status' => 'error', 'message' => 'Something went wrong.'];
        }
    }

    public function enableDisableOfferCommunicationLog(Request $request)
    {
        $data = $this->studentCourseService->enableDisableOfferCommunicationLog($request->input());
        if ($data) {
            return ['status' => 'success', 'message' => 'Update Successfully.'];
        } else {
            return ['status' => 'error', 'message' => 'Something went wrong.'];
        }
    }

    public function getStudentCOEDetails(Request $request)
    {
        $resultArr = $this->studentCourseService->getStudentCOEDetails($request);

        return $this->successResponse('Data found successfully', 'data', $resultArr);
    }

    public function getContractScheduleData(Request $request)
    {
        $resultArr = $this->studentCourseService->getContractScheduleData($request->input());

        return $this->successResponse('Data found successfully', 'data', $resultArr);
    }

    public function saveStudentCOEDetails(Request $request, AddCoeForOfferProcess $process)
    {
        DB::beginTransaction();
        try {
            $data = $process->run($request);
            DB::commit();

            return $this->successResponse('Uploaded Completed', 'data', '', 200);
        } catch (\Exception $e) {
            DB::rollBack();
            // safeDD($e);
            throw new ApplicationException($e->getMessage());
        }

    }

    public function getOfferScheduleData(Request $request)
    {
        $data = $this->studentCourseService->getOfferScheduleData($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function saveOfferScheduleData(Request $request, ManageOfferScheduleProcess $process)
    {
        /* TODO: rules here or create request class and handle there */
        $this->validate($request, [
            'installment_start_date' => 'required',
            'remaining_amount' => 'required|numeric|gt:0',
            'no_of_frequency' => 'required',
            'no_of_installment' => 'required',
            'day_frequency' => 'required',
        ], [
            'no_of_frequency.required' => 'Please specify how often the student will pay.',
            'no_of_installment.required' => 'Please select installment',
            'day_frequency.required' => 'Please select commission period',
        ]);

        DB::beginTransaction();
        try {
            $process->run($request);
            /*$data = $process->run(ReschedulePayload::LazyFromArray([
                'request' => $request,
            ]));*/
            DB::commit();

            return $this->successResponse('Fee schedule update successfully', 'data', '', 200);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }

    }

    public function getCoursesListFromType(Request $request)
    {
        $courseTypeId = $request->input('course_type_id');

        $courseSelect = $this->getCourseSelectOptions($courseTypeId);
        $intakeSelect = $this->getIntakeSelectOptions($courseTypeId);
        $campusSelect = $this->getCampusSelectOptions($courseTypeId);

        return $this->successResponse('Data found successfully', 'data', [
            'courseSelect' => $courseSelect,
            'courseIntake' => $intakeSelect['Intakes'],
            'intakeYearSelect' => $intakeSelect['IntakeYears'],
            'courseCampus' => $campusSelect,
        ]);
    }

    private function getCourseSelectOptions($courseTypeId): array
    {
        $studentCourses = StudentCourses::where('course_type_id', $courseTypeId)
            ->with('course')
            ->select('course_id')
            ->get();

        $courses = $studentCourses->filter(fn ($c) => $c->course)
            ->mapWithKeys(fn ($c) => [
                $c->course_id => $c->course->course_code.': '.$c->course->course_name,
            ])->toArray();

        return [0 => 'All'] + $courses;
    }

    private function getIntakeSelectOptions(int $courseTypeId): array
    {
        $intakes = DB::table('rto_student_courses')
            ->join('rto_course_intake_dates', 'rto_student_courses.intake_id', '=', 'rto_course_intake_dates.id')
            ->where('rto_student_courses.course_type_id', $courseTypeId)
            ->select([
                'rto_course_intake_dates.id as intake_id',
                'rto_course_intake_dates.intake_name',
                'rto_course_intake_dates.intake_start',
                'rto_course_intake_dates.intake_end',
            ])
            ->get();

        $options = $intakes->mapWithKeys(fn ($intake) => [
            $intake->intake_id => "{$intake->intake_name} ({$intake->intake_start} - {$intake->intake_end})",
        ])->toArray();

        $data['Intakes'] = [0 => 'All'] + $options;

        $years = $intakes->pluck('intake_year')->unique()->sort()->values()->mapWithKeys(
            fn ($year) => [$year => $year]
        )->toArray();

        $data['IntakeYears'] = [0 => 'All'] + $years;

        return $data;
    }

    private function getCampusSelectOptions(int $courseTypeId): array
    {
        $campuses = DB::table('rto_student_courses')
            ->join('rto_campus_intakes', 'rto_campus_intakes.intake_id', '=', 'rto_student_courses.intake_id')
            ->leftJoin('rto_campus', 'rto_campus.id', '=', 'rto_campus_intakes.campus_id')
            ->where('rto_student_courses.course_type_id', $courseTypeId)
            ->select('rto_campus.id', 'rto_campus.name')
            ->distinct()
            ->get();

        $options = $campuses->mapWithKeys(fn ($campus) => [
            $campus->id => $campus->name,
        ])->toArray();

        return [0 => 'All'] + $options;
    }

    public function getIntakeDetails(Request $request)
    {
        $courseId = $request->input('course_id');

        $intakeData = $this->getIntakeOptions($courseId);
        $intakeSelect = $this->formatIntakeOptions($intakeData);
        $intakeYearSelect = $this->formatIntakeYears($intakeData);
        $campusSelect = $this->getCampusOptions($courseId);

        return $this->successResponse('Data found successfully', 'data', [
            'courseIntake' => $intakeSelect,
            'courseCampus' => $campusSelect,
            'intakeYearSelect' => $intakeYearSelect,
        ]);
    }

    private function getIntakeOptions(?int $courseId): \Illuminate\Support\Collection
    {
        return DB::table('rto_student_courses')
            ->join('rto_course_intake_dates', 'rto_student_courses.intake_id', '=', 'rto_course_intake_dates.id')
            ->when($courseId, fn ($q) => $q->where('rto_student_courses.course_id', $courseId))
            ->select([
                'rto_course_intake_dates.id as intake_id',
                'rto_course_intake_dates.intake_name',
                'rto_course_intake_dates.intake_year',
                'rto_course_intake_dates.intake_start',
                'rto_course_intake_dates.intake_end',
            ])
            ->distinct()
            ->get();
    }

    private function formatIntakeOptions(\Illuminate\Support\Collection $intakes): array
    {
        $options = $intakes->mapWithKeys(fn ($i) => [
            $i->intake_id => "{$i->intake_name} ({$i->intake_start} - {$i->intake_end})",
        ])->toArray();

        return [0 => 'All'] + $options;
    }

    private function formatIntakeYears(\Illuminate\Support\Collection $intakes): array
    {
        $years = $intakes->pluck('intake_year')->unique()->sort()->values()->mapWithKeys(
            fn ($year) => [$year => $year]
        )->toArray();

        return [0 => 'All'] + $years;
    }

    private function getCampusOptions(?int $courseId): array
    {
        $campuses = DB::table('rto_student_courses')
            ->join('rto_campus_intakes', 'rto_campus_intakes.intake_id', '=', 'rto_student_courses.intake_id')
            ->leftJoin('rto_campus', 'rto_campus.id', '=', 'rto_campus_intakes.campus_id')
            ->when($courseId, fn ($q) => $q->where('rto_student_courses.course_id', $courseId))
            ->where('rto_campus.status', 1)
            ->select('rto_campus.id', 'rto_campus.name')
            ->distinct()
            ->get();

        $options = $campuses->mapWithKeys(fn ($c) => [$c->id => $c->name])->toArray();

        return [0 => 'All'] + $options;
    }

    public function getIntakeDetailsFromYear(Request $request)
    {
        $courseId = $request->input('course_id');
        $intakeYear = $request->input('intake_year');

        if (! $courseId || ! $intakeYear) {
            return $this->errorResponse('Course ID and Intake Year are required', [], 422);
        }

        $intakes = $this->fetchIntakesByYear($courseId, $intakeYear);
        $intakeSelect = $this->formatIntakeOptions($intakes);

        return $this->successResponse('Data found successfully', 'data', [
            'courseIntake' => $intakeSelect,
        ]);
    }

    private function fetchIntakesByYear(int $courseId, int $intakeYear): \Illuminate\Support\Collection
    {
        return DB::table('rto_student_courses')
            ->join('rto_course_intake_dates', 'rto_student_courses.intake_id', '=', 'rto_course_intake_dates.id')
            ->where('rto_student_courses.course_id', $courseId)
            ->where('rto_course_intake_dates.intake_year', $intakeYear)
            ->select([
                'rto_course_intake_dates.id as intake_id',
                'rto_course_intake_dates.intake_name',
                'rto_course_intake_dates.intake_year',
                'rto_course_intake_dates.intake_start',
                'rto_course_intake_dates.intake_end',
            ])
            ->distinct()
            ->get();
    }
}
