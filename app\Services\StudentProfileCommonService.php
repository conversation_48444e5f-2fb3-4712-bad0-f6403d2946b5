<?php

namespace App\Services;

use App\DTO\studentProfile\SaveEnrollStudentCourse;
use App\Helpers\Helpers;
use App\Model\v2\CollegeCampus;
use App\Model\v2\Colleges;
use App\Model\v2\ContractFundingSource;
use App\Model\v2\CourseType;
use App\Model\v2\Employer;
use App\Model\v2\Student;
use App\Model\v2\StudentCertificateRegister;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudyReason;
use App\Repositories\StudentProfileCommonRepository;
use App\Repositories\StudentProfileRepository;
use App\Repositories\StudentSummaryTabRepository;
use App\Traits\CommonTrait;
use App\Traits\SendNotificationTrait;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Notifications\Types\NotificationType;
use Support\Services\UploadService;

class StudentProfileCommonService
{
    use CommonTrait;
    use SendNotificationTrait;

    private $studentProfileCommonRepository;

    private $studentProfile;

    private $studentSummaryTabRepository;

    public function __construct(
        StudentProfileCommonRepository $studentProfileCommonRepository,
        StudentSummaryTabRepository $studentSummaryTabRepository,
        Student $student
    ) {
        $this->studentProfileCommonRepository = $studentProfileCommonRepository;
        $this->studentSummaryTabRepository = $studentSummaryTabRepository;
        $this->studentProfile = new StudentProfileRepository($student);
    }

    public function uploadFile($file)
    {
        $filePath = Config::get('constants.uploadFilePath.StudentNotes');
        $destinationPath = Helpers::changeRootPath($filePath);
        $time = time();
        $originalName = $file->getClientOriginalName();
        $filenm = hashFileName($originalName);
        $fileName = $this->removeSpecialCharacter($filenm);
        //        $upload_success = $file->move($destinationPath['default'], $fileName);
        //        dd($destinationPath);
        $upload_success = UploadService::uploadAs($destinationPath['default'], $file, $fileName);

        info('file uploaded From notes', [$upload_success]);
        if ($upload_success) {
            return [
                'path' => $destinationPath['view'],
                'name' => $fileName,
            ];
        }

        return null;
    }

    private function removeSpecialCharacter($fileName)
    {
        $fileName = str_replace(' ', '-', $fileName); // Replaces all spaces with hyphens.

        return preg_replace('/[^A-Za-z0-9.\-]/', '', $fileName); // Removes special chars.
    }

    public function createLog($request)
    {
        $file = $request->file('note_attachment1');
        $note_attachment_path = '';
        $note_attachment = '';
        if (! empty($file)) {
            $fileUploadResult = $this->uploadFile($file);
            if ($fileUploadResult) {
                $note_attachment_path = $fileUploadResult['path'];
                $note_attachment = $fileUploadResult['name'];
            }
        }
        $logData = [
            'college_id' => $request->user()->college_id,
            'comment_by' => $request->user()->id,
            'student_id' => $request->input('note_student_id'),
            'today_date' => date('l,d F Y'),
            'type' => '',
            'log_type' => 'note',
            'status' => '',
            'log' => $request->input('log'),
            'activity_log' => 'note',
            'note_attachment_path' => $note_attachment_path,
            'note_attachment' => $note_attachment,
            'view_by' => '',
            'visiblity' => 1,
            'created_by' => $request->user()->id,
            'updated_by' => $request->user()->id,
        ];

        return $this->studentProfileCommonRepository->saveActivityNote($logData);
    }

    public function manageCourseStatusHistory($studentId, $courseId, $newStatus, $oldStatus)
    {
        $dataArr = Auth::user();
        if (! empty($newStatus) && $oldStatus != $newStatus) {
            $dataArr = [
                'college_id' => $dataArr->college_id,
                'student_id' => $studentId,
                'course_id' => $courseId, //  student Course Id.
                'status_from' => empty($oldStatus) ? 'New Application Request' : $oldStatus,
                'status_to' => $newStatus,
                'today_date' => date('d/m/Y h:i:s A'),
                'status' => ($oldStatus == '') ? ("Inserted record with status : $newStatus") : ("Convert from $oldStatus to $newStatus"),
                'created_by' => $dataArr->id,
                'updated_by' => $dataArr->id,
            ];
            $this->studentProfileCommonRepository->createCourseStatusHistory($dataArr);
        }
    }

    public function getStatusHistoryData($collegeId, $studentId, $courseId)
    {
        $data = $this->studentProfileCommonRepository->getStatusHistoryData($collegeId, $studentId, $courseId);
        foreach ($data as $key => $value) {
            $data[$key]['createAt'] = Helpers::convertDateTimeToReadableFormat($value['created_at']);
        }

        return $data;
    }

    public function getStudentCoursesData($studentId, $addMore = false, $orderByDate = false)
    {
        $courseDetail = $this->studentProfileCommonRepository->getStudentCoursesData($studentId, true, $orderByDate);
        if ($addMore) {
            $courseDetailFirst = [
                ['id' => 0, 'course_title' => '+ Enroll to New Course'],
            ];

            return array_merge($courseDetailFirst, $courseDetail);
        }

        return $courseDetail;
    }

    public function getOfferIdList($studentId, $isNewOffer)
    {
        $offerIdList = $this->studentProfileCommonRepository->getOfferIdList($studentId);
        if ($isNewOffer) {
            $newOptionArr = [['Id' => 'new_offer', 'Name' => 'New Offer']];
            $offerIdList = array_merge($offerIdList, $newOptionArr);
        }

        return $offerIdList;
    }

    public function getCourseList($collegeId, $courseTypeID, $campusId)
    {
        $courses = $this->studentProfileCommonRepository->getCourses($collegeId, $courseTypeID, $campusId);
        $courseList = [];
        foreach ($courses as $course) {
            $courseList[] = [
                'Id' => $course['id'],
                'Name' => $course['course_code'].' : '.$course['course_name'],
            ];
        }

        return $courseList;
    }

    public function getCourseWiseList($campusId)
    {
        return $this->studentProfileCommonRepository->getCoursesCampusWise($campusId);
    }

    public function getCollegeCampusList($collegeId, $courseId)
    {
        return $this->studentProfileCommonRepository->getCollegeCampusList($collegeId, $courseId);
    }

    public function getResultCalculationMethods($courseId)
    {
        $arrResultsCalculationMethod = Config::get('constants.arrResultsCalculationMethod');
        $data = [];
        if ($courseId != null) {
            $course = $this->studentProfileCommonRepository->getResultCalculationMethod($courseId);
            if (! empty($course)) {
                $resultsCalculationMethod = $course[0]['results_calculation_methods'];
                $arrResultsCalculationMethod = [$resultsCalculationMethod => $arrResultsCalculationMethod[$resultsCalculationMethod]];
            }
        }
        foreach ($arrResultsCalculationMethod as $key => $value) {
            $data[] = ['Id' => $key, 'Name' => $value];
        }

        return $data;
    }

    public function getAgentsByCollegeId($collegeId)
    {
        return $this->studentProfileCommonRepository->getAgentsByCollegeId($collegeId);
    }

    public function getCourseTemplates($collegeId, $courseId)
    {
        if ($courseId == null) {
            return $this->studentProfileCommonRepository->getTemplatesByCollegeId($collegeId);
        } else {
            return $this->studentProfileCommonRepository->getTemplatesByCollegeAndCourseId($collegeId, $courseId);
        }
    }

    public function getStudyReasonList(): array
    {
        return $this->studentProfileCommonRepository->getStudyReasons();
    }

    public function enrollStudentCourse(SaveEnrollStudentCourse $data)
    {
        $post = $data->toArray();
        $studentId = $post['student_id'];
        $campusId = $post['campus_id'];
        $courseId = $post['course_id'];
        $offerId = $post['offer_id'];
        $userId = Auth::user()->id;
        DB::beginTransaction();
        try {
            if (! empty($post['agent_id']) && ! empty($campusId) && ! empty($courseId)) {
                $rowCount = $this->studentProfileCommonRepository->checkDuplicateRecord($studentId, $campusId, $courseId, $offerId);
                if ($rowCount > 0) {
                    return ['status' => 'error', 'message' => "Duplicate data can't be accepted"];
                }
                $studentOfferID = $this->studentProfileCommonRepository->addStudentOffer($userId, $studentId, $post);
                $courseTypeData = $this->studentProfileCommonRepository->findCoursesData($courseId);
                $post['offer_id'] = (! empty($studentOfferID)) ? $studentOfferID : null;
                $post['course_type_id'] = (isset($courseTypeData)) ? $courseTypeData->course_type_id : null;
                $studentCourseData = $this->setCourseEnrollDataArr($post, 'add');

                $createRes = $this->studentProfileCommonRepository->createStudentCourses($studentCourseData);
                if ($createRes) {
                    $studentDetail = Student::find($post['student_id']);
                    $studentDetail->updated_at = date('Y-m-d H:i:s');
                    $studentDetail->save();
                    $this->manageCourseStatusHistory($studentId, $courseId, $post['status'], '');
                    if ($studentCourseData['course_template'] != '') {

                        $post['student_course_id'] = $createRes->id;
                        $post['student_id'] = $createRes->student_id;
                        $post['course_id'] = $createRes->course_id;
                        $post['college_id'] = Auth::user()->college_id;

                        $this->studentProfileCommonRepository->enrollTemplateUnit($post);
                    }
                    DB::commit();

                    // Send COURSE_ENROLLED notification to student
                    $studentId = $createRes->student_id;
                    $studentUser = Student::find($studentId)->associatedUserAccount;
                    $adminUser = Auth::user();
                    $this->sendNotificationsToUsers([
                        [
                            'user' => $studentUser,
                            'type' => NotificationType::COURSE_ENROLLED,
                            'data' => $createRes->id,
                        ],
                        [
                            'user' => $adminUser,
                            'type' => NotificationType::ADMIN_COURSE_ENROLLED,
                            'data' => $createRes->id,
                        ],
                    ]);

                    return ['status' => 'success', 'message' => 'Save successfully.', 'id' => $createRes->id];
                }
            }
        } catch (\Exception $e) {
            DB::rollBack();

            return ['status' => 'error', 'message' => $e->getMessage()];
            // throw new ApplicationException($e->getMessage());
        }
    }

    private function setCourseEnrollDataArr($post, $type)
    {
        $userId = Auth::user()->id;
        $total_weeks = round(abs(strtotime($post['finish_date']) - strtotime($post['start_date'])) / 604800);
        $resData = $this->buildBaseData($post, $total_weeks);
        if ($type == 'add') {
            $this->addAdditionalData($resData, $post, $userId);
        }

        return $resData;
    }

    private function formatDate($date, $format = 'Y-m-d')
    {
        return $date ? date($format, strtotime($date)) : null;
    }

    private function buildBaseData($post, $total_weeks)
    {
        $userId = Auth::user()->id;

        return [
            'offer_id' => $post['offer_id'] ?? null,
            'agent_id' => $post['agent_id'] ?? null,
            'campus_id' => $post['campus_id'] ?? null,
            'intake_year' => $post['intake_year'] ?? null,
            'intake_date' => $this->formatDate($post['intake_date']),
            'issued_date' => $this->formatDate($post['issued_date']),
            'start_date' => $this->formatDate($post['start_date']),
            'finish_date' => $this->formatDate($post['finish_date']),
            'census_date1' => $this->formatDate($post['census_date1']),
            'census_date2' => $this->formatDate($post['census_date2']),
            'census_date3' => $this->formatDate($post['census_date3']),

            'course_template' => $post['course_template'] ?? '',
            'total_weeks' => ($total_weeks > 0) ? $total_weeks : '0',
            'course_duration_type' => $post['course_duration_type'] ?? null,
            'default_unit_fee' => $post['default_unit_fee'] ?? null,
            'is_finish_dt' => $post['is_finish_dt'] ?? '0',
            'course_fee' => $post['course_fee'] ?? null,
            'enroll_fee' => $post['enroll_fee'] ?? null,
            'course_upfront_fee' => $post['course_upfront_fee'] ?? null,
            'course_material_fee' => $post['course_material_fee'] ?? null,
            'status' => $post['status'] ?? '',
            'study_reason_id' => $post['study_reason_id'] ?? null,
            'application_request' => $post['application_req'] ?? '',
            'credit_transfer_request' => $post['credit_transfer'] ?? null,
            'special_instructions' => $post['special_instructions'] ?? '',
            'is_orientation' => $post['is_orientation'] ?? '0',
            'survey_contact_status' => $post['survey_contact_status'] ?? null,
            'purchasing_contract_identifier' => $post['purchasing_contract_identifier'] ?? null,
            'associated_course_identifier' => $post['associated_course_identifier'] ?? null,
            'is_fulltimelearing' => $post['is_full_time_learning'] ?? null,
            'employer_id' => $post['employer_id'] ?? null,
            'contract_schedule_id' => $post['contract_schedule_id'] ?? null,
            'purchasing_contract_ode' => $post['purchasing_contract_ode'] ?? 0,
            'schedule_code' => $post['schedule_code'] ?? null,
            'auto_update_to_sru' => $post['auto_update_to_sru'] ?? 0,

            'internal' => isset($post['is_internal']) && $post['is_internal'] ? 'Y' : 'N',
            'external' => isset($post['is_external']) && $post['is_external'] ? 'Y' : 'N',
            'workplace_based_delivery' => isset($post['workplace_based_delivery']) && $post['workplace_based_delivery'] ? 'Y' : 'N',
            'is_claim' => isset($post['is_claim']) && $post['is_claim'] ? $post['is_claim'] : '0',
            'advance_standing_credit' => isset($post['advance_standing_credit']) && $post['advance_standing_credit'] ? $post['advance_standing_credit'] : '0',

            'purchasing_contract_schedule_identifier' => $post['purchasing_contract_schedule_identifier'] ?? null,
            'is_material_fee_inc_initail_payment' => isset($post['is_material_fee_inc_initail_payment']) && $post['is_material_fee_inc_initail_payment'] ? $post['is_material_fee_inc_initail_payment'] : '0',

            'is_qualification' => isset($post['is_qualification']) && $post['is_qualification'] ? $post['is_qualification'] : '0',
            'is_certificate' => '0', // isset($post['is_certificate']) && $post['is_certificate'] ? $post['is_certificate'] : '0',
            'mode_of_delivery' => isset($post['mode_of_delivery']) && $post['mode_of_delivery'] ? $post['mode_of_delivery'] : null,
            'updated_by' => $userId,

            'wil_requirements' => ($post['wil_requirements']) ? $post['wil_requirements'] : null,
            'third_party_providers' => ($post['third_party_providers']) ? $post['third_party_providers'] : null,
            'scholarship_percentage' => ($post['scholarship_percentage']) ? $post['scholarship_percentage'] : null,
            // 'is_receiving_any_scholarship'  => ($post['is_receiving_any_scholarship']) ? $post['is_receiving_any_scholarship'] : 'no',
            'is_receiving_any_scholarship' => isset($post['is_receiving_any_scholarship']) && $post['is_receiving_any_scholarship'] ? 'yes' : 'no',
            'subject_clming_for_credit' => ($post['subject_clming_for_credit']) ? $post['subject_clming_for_credit'] : null,
        ];
    }

    private function addAdditionalData(&$resData, $post, $userId)
    {
        $resData['student_id'] = $post['student_id'];
        $resData['course_id'] = $post['course_id'];
        $resData['offer_id'] = $post['offer_id'];
        $resData['offer_status'] = 'Enrolled';
        $resData['course_type_id'] = $post['course_type_id'];
        $resData['res_cal_method'] = $post['res_cal_method'] ?? null;
        $resData['created_by'] = $userId;
    }

    private function isEnrolledInCourse($student, $courseId, $studentCourseId)
    {
        foreach ($student->studentcourses as $scInfo) {
            if ($courseId == $scInfo->course_id || $studentCourseId == $scInfo->id) {
                return true;
            }
        }

        return false;
    }

    private function getProfilePicUrl($profile_pic, $viewPath, $profile_picture)
    {
        if (file_exists($profile_pic) && ! empty($profile_picture)) {
            return asset($viewPath.$profile_picture);
        } else {
            return '';
        }
    }

    public function getStudentDetail($studentId, $collegeId, $studentCourseId)
    {
        $studentDetail = $data['student_detail'] = $this->studentProfileCommonRepository->getStudentDetails($studentId);
        $data['student_detail']['profile_pic'] = $this->getStudentProfilePicPath($studentDetail['id'], $studentDetail['profile_picture']);
        $data['student_detail']['country'] = $this->studentProfile->getCountryData($studentDetail['college_id']);
        $offerIdList = StudentCourses::where(['student_id' => $studentId])->groupBy('offer_id')->select(['offer_id as Id', 'offer_id as Name'])->get()->toArray();
        $data['coursesData']['data'] = $this->studentProfileCommonRepository->getCourses();
        $data['campusData']['data'] = CollegeCampus::select('name as Name', 'id as Id')->where('status', 1)->get()->toArray();
        $data['resultData']['data'] = $this->convertConstantsFormat(Config::get('constants.arrResultsCalculationMethod'));
        $data['agentsData']['data'] = $this->studentProfileCommonRepository->getAgentsByCollegeId($collegeId);
        $data['intakeYearData']['data'] = $this->convertConstantsFormat(Config::get('constants.arrIntakeYear'));
        $data['templateData']['data'] = $this->studentProfileCommonRepository->getTemplatesByCollegeId($collegeId);
        $data['surveyContactData']['data'] = $this->convertConstantsFormat(Config::get('constants.arrSurveyContactStatusDiv'));
        $data['courseStatusData']['data'] = $this->convertConstantsFormat(Config::get('constants.arrCourseStatus'));
        $data['courseDurationType']['data'] = $this->convertConstantsFormat(Config::get('constants.arrCourseDurationType'));
        $data['studyReasonData']['data'] = StudyReason::select('avaitmiss_id as Id', 'title as Name')->orderBy('avaitmiss_id', 'ASC')->get()->toArray();
        $data['employerData']['data'] = Employer::select('employer_name as Name', 'id as Id')->get()->toArray();
        $data['contractSchedule']['data'] = $this->getContractCodeList($collegeId);
        $data['subjectCredit']['data'] = $this->convertConstantsFormat(Config::get('constants.arrSubjectCalmingForCredit'));
        $data['student_detail']['isHigherEd'] = StudentCourses::checkCourseIsHigherEd($studentCourseId);
        $typeArr = Config::get('constants.generateCertificateListType');
        $course_id = StudentCourses::where('id', $studentCourseId)->value('course_id');
        $data['isCertificateGenerated'] = StudentCertificateRegister::where(['rto_student_certificate_register.college_id' => $collegeId, 'student_id' => $studentId, 'course_id' => $course_id, 'certificate_type' => $typeArr['C']])->count();
        if ($data['isCertificateGenerated'] > 0) {
            $data['certificate_data'] = StudentCertificateRegister::leftjoin('rto_users', 'rto_student_certificate_register.created_by', '=', 'rto_users.id')
                ->where(['rto_student_certificate_register.college_id' => $collegeId, 'student_id' => $studentId, 'course_id' => $course_id, 'certificate_type' => $typeArr['C']])
                ->orderBy('rto_student_certificate_register.id', 'desc')
                ->select('rto_student_certificate_register.*', 'rto_users.name', DB::raw("DATE_FORMAT(date_generated, '%d-%m-%Y') AS date_generated"))->first()->toArray();
        } else {
            $data['certificate_data'] = [];
        }

        $newOptionArr = [['Id' => 'new_offer', 'Name' => 'New Offer']];
        if ($offerIdList) {
            $newOptionArr = [['Id' => 'new_offer', 'Name' => 'New Offer']];
            $data['offerIdData']['data'] = array_merge($offerIdList, $newOptionArr);
        }

        return $data;
    }

    public function getContractCodeList($collegeId)
    {

        $arrFundingSource = Config::get('constants.arrFundingSource');
        $resultArr[0]['Name'] = 'No Contract Schedule Found';
        $resultArr[0]['Id'] = 0;
        $arrContractCode = ContractFundingSource::from('rto_contract_funding_source as rcfs')
            ->join('rto_contract_code as rcc', 'rcc.id', '=', 'rcfs.contract_code_id')
            ->where('rcfs.college_id', $collegeId)
            ->get(['rcfs.*', 'rcc.contract_code']);
        foreach ($arrContractCode as $key => $row) {
            $resultArr[$key]['Name'] = $row->contract_code.' ['.$arrFundingSource[$row->funding_source].']';
            $resultArr[$key]['Id'] = $row->id;
        }

        return $resultArr;
    }

    public function updateStudentDetails($studentId, $formData)
    {// TODO::NOT USED
        $arrData = [];
        parse_str($formData, $arrData);
        // unset($arrData['full_name'],$arrData['profile_pic'],$arrData['birth_country_name'],$arrData['country_of_birth'],$arrData['birth_nationality_name']);
        // print_r($arrData);
        exit;
        $updatedStudent = $this->studentProfileCommonRepository->updateStudent($studentId, $arrData);

        return $arrData;
    }

    public function uploadStudentProfilePic($collegeId, $studentId, $file, $originalFileName)
    {
        $filePath = Config::get('constants.uploadFilePath.StudentPics');
        $destinationPath = $this->changeRootPath($filePath, $collegeId, $studentId);
        $studPicName = str_replace(' ', '_', $originalFileName);
        $filename = 'student-'.$studentId.'-'.hashFileName($studPicName);

        $this->generateThumbImageS3($file, $destinationPath, $filename);
        // $res = $file->move($destinationPath['default'], $filename);
        $res = UploadService::uploadAs($destinationPath['view'], $file, $filename);
        info('Student profile pic update', [$res]);
        if ($res && ! empty($studentId)) {
            $this->studentProfileCommonRepository->updateProfilePicture($studentId, $filename);

            return ['uploaded' => true, 'fileUid' => $filename, 'status' => 'success', 'message' => 'Student image uploaded successfully.'];
        }

        return ['status' => 'error', 'message' => 'Something went wrong. Please try again.'];
    }

    public function getAllCourseList($request)
    {
        return StudentCourses::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where(['rto_courses.status' => 1, 'rto_courses.activated_now' => 1, 'student_id' => $request['student_id']])
            ->select('rto_courses.id as Id', DB::raw('CONCAT(rto_courses.course_code,":",rto_courses.course_name) AS Name'))
            ->groupBy('rto_courses.id')->get()->toArray();

    }

    public function isActiveStudentCourse($studentCourseId)
    {
        $resData = StudentCourses::find($studentCourseId);
        if (isset($resData)) {
            return $this->isActiveCourse($resData->status);
        }

        return false;
    }

    public function verifyCourseTypeAndStatus($studentCourseId)
    {
        $resData = StudentCourses::find($studentCourseId);
        if (isset($resData)) {
            return [
                'isHigherEd' => $this->checkCourseTypeHigherEd($resData->course_type_id),
                'isActiveCourse' => $this->isActiveCourse($resData->status),
            ];
        }

        return ['isHigherEd' => false, 'isActiveCourse' => false];
    }

    public function isActiveCourse($status)
    {
        $inactiveStatusList = [
            StudentCourses::STATUS_CANCELLED,
            // StudentCourses::STATUS_WITHDRAWN,
            // StudentCourses::STATUS_DEFERRED,
            // StudentCourses::STATUS_SUSPENDED,
        ];

        return ! in_array($status, $inactiveStatusList);
    }

    public function checkCourseTypeHigherEd($courseTypeId)
    {
        if (! empty($courseTypeId)) {
            $courseTypeName = CourseType::find($courseTypeId)->title;
            if (isset($courseTypeName) && ! empty($courseTypeName)) {
                $arrSpecialGradeTypeList = Config::get('constants.arrSpecialCourseTypeName');
                $isHigherGradeType = ($courseTypeName != str_ireplace($arrSpecialGradeTypeList, 'XX', $courseTypeName)) ? true : false;

                return $isHigherGradeType;
            }
        }

        return false;
    }

    public function getUserAuditLogData($request)
    {
        $result = $this->studentProfileCommonRepository->getUserAuditLogData($request);

        return [
            'data' => $result['data'],
            'total' => $result['total'],
        ];
    }

    public function getCollegeEmailList($collegeId)
    {
        $result = Colleges::join('rto_college_details as rcd', 'rcd.college_id', '=', 'rto_colleges.id')
            ->where('rto_colleges.id', '=', $collegeId)
            ->select(
                'rto_colleges.contact_email',
                'rcd.marketing_email',
                'rcd.orgazination_id',
                'rcd.admission_email',
                'rcd.it_email',
                'rcd.acedemic_email',
                'rcd.account_email'
            )->first();

        $emailTypes = [
            'acedemic_email' => 'Academic Email',
            'account_email' => 'Account Email',
            'admission_email' => 'Admission Email',
            'contact_email' => 'Contact Email',
            'marketing_email' => 'Marketing Email',
            'it_email' => 'IT Email',
        ];

        $arrList = [];

        foreach ($emailTypes as $key => $label) {
            if (! empty($result[$key])) {
                $arrList[] = [
                    'value' => $result[$key],
                    'text' => "$label ($result[$key])",
                ];
            }
        }

        return $arrList;
    }

    public function getOnlyStudentDetail($studentId, $collegeId, $studentCourseId)
    {
        $studentDetail = $data = $this->studentProfileCommonRepository->getStudentDetails($studentId);
        $data['profile_pic'] = $this->getStudentProfilePicPath($studentDetail['id'], $studentDetail['profile_picture']);
        $data['country'] = $this->studentProfile->getCountryData($studentDetail['college_id']);

        return $data;
    }

    public function canExtendCourseDateRange($status)
    {
        $eligibleStatuses = [
            StudentCourses::STATUS_ENROLLED,
            StudentCourses::STATUS_CURRENT_STUDENT,
        ];

        return in_array($status, $eligibleStatuses);
    }
}
